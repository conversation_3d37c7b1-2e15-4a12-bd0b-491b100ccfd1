datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider = "prisma-client-js"
}

model User {
    id            String          @id @default(cuid())
    name          String?
    email         String?         @unique
    emailVerified DateTime?
    image         String?
    accounts      Account[]
    sessions      Session[]
    corporateId   String?
    fullName      String?
    teamId        String?
    systemRole    SystemRole      @default(USER)
    // Optional for WebAuthn support
    Authenticator Authenticator[]

    createdAt  DateTime     @default(now())
    updatedAt  DateTime     @updatedAt
    GameRecord GameRecord[]
    Coupon     Coupon[]

    nickname         String?
    luckyDrawName    String?
    luckyDrawAge     Int?
    luckyDrawAddress String?
    luckyDrawPhone   String?
    banned           Boolean     @default(false)
    PlayCount        PlayCount[]
}

model PlayCount {
    id     String @id @default(cuid())
    User   User   @relation(fields: [userId], references: [id])
    userId String
    gameId String
    count  Int
}

model Account {
    userId            String
    type              String
    provider          String
    providerAccountId String
    refresh_token     String?
    access_token      String?
    expires_at        Int?
    token_type        String?
    scope             String?
    id_token          String?
    session_state     String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@id([provider, providerAccountId])
}

model Session {
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model VerificationToken {
    identifier String
    token      String
    expires    DateTime

    @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
    credentialID         String  @unique
    userId               String
    providerAccountId    String
    credentialPublicKey  String
    counter              Int
    credentialDeviceType String
    credentialBackedUp   Boolean
    transports           String?

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@id([userId, credentialID])
}

enum SystemRole {
    USER
    ADMIN
    DEVELOPER
}

model Game {
    id         String       @id @default(cuid())
    name       String
    createdAt  DateTime     @default(now())
    startTime  DateTime
    endTime    DateTime
    GameRecord GameRecord[]
}

model GameRecord {
    id String @id @default(cuid())

    gameId String
    game   Game   @relation(fields: [gameId], references: [id], onDelete: Cascade)

    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    score Decimal

    drawResult DrawResult?
    couponId   String?
    coupon     Coupon?     @relation(fields: [couponId], references: [id], onDelete: SetNull)
}

model WebsiteConfig {
    id              String   @id @default(cuid())
    name            String
    startDate       DateTime
    endDate         DateTime
    luckyDrawChance Float    @default(0.0)

    revealDate      DateTime?
    prizeOwnerName  String?
    prizeOwnerPhone String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Coupon {
    id        String   @id @default(cuid())
    code      String   @unique
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    userId     String?
    User       User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
    GameRecord GameRecord[]
}

// DrawResult
enum DrawResult {
    WIN
    LOSE
}
