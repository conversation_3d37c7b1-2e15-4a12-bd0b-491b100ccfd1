import { prisma } from "@/libs/prisma";

export const query = ({ gameId }: { gameId: string }) =>
  prisma.gameRecord.findMany({
    where: {
      gameId,
    },
    include: {
      user: {
        select: {
          id: true,
          nickname: true,
        },
      },
    },
    orderBy: [
      {
        score: "desc",
      },
      {
        createdAt: "desc",
      },
    ],
    take: 10,
    distinct: ["userId"],
  });
