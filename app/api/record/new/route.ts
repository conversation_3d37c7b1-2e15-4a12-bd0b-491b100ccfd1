import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

// Game-specific score limits and time constraints
const GAME_LIMITS = {
  balance: {
    maxScore: 60,
    minTime: 100,
    maxTime: 65000,
  },
  catch: {
    maxScore: 5000,
    minTime: 55000,
    maxTime: 65000,
  },
  quiz: {
    maxScore: 100,
    minTime: 1000,
    maxTime: 65000,
  },
} as const;

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { gameId, score } = body;

    // Validate required fields
    if (!gameId || score === undefined) {
      return NextResponse.json({ message: "缺少必要參數" }, { status: 400 });
    }

    // Validate game ID
    if (!GAME_LIMITS[gameId as keyof typeof GAME_LIMITS]) {
      return NextResponse.json({ message: "無效的遊戲ID" }, { status: 400 });
    }

    const limits = GAME_LIMITS[gameId as keyof typeof GAME_LIMITS];

    // Validate score limits
    if (score < 0 || score > limits.maxScore) {
      // Log suspicious score
      console.warn(`Suspicious score submitted by user ${userId}`, {
        gameId,
        score,
        maxAllowed: limits.maxScore,
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        { message: "分數超出合理範圍" },
        { status: 400 },
      );
    }

    // Create the game record
    const gameRecord = await prisma.gameRecord.create({
      data: {
        gameId,
        userId,
        score,
      },
    });

    return NextResponse.json({
      id: gameRecord.id,
      message: "記錄創建成功",
    });
  } catch (error) {
    console.error("Error creating game record:", error);
    return NextResponse.json(
      {
        message: "伺服器錯誤，請稍後再試",
      },
      { status: 500 },
    );
  }
}
