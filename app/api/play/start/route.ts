import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { gameId } = body;

    if (!gameId) {
      return NextResponse.json({ message: "缺少遊戲ID" }, { status: 400 });
    }

    // Validate game ID
    const validGameIds = ["balance", "catch", "quiz"];
    if (!validGameIds.includes(gameId)) {
      return NextResponse.json({ message: "無效的遊戲ID" }, { status: 400 });
    }

    // Check if user is banned
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { banned: true },
    });

    if (user?.banned) {
      return NextResponse.json({ message: "帳號已被停用" }, { status: 403 });
    }

    // Find existing play count record for this user and game
    const existingPlayCount = await prisma.playCount.findFirst({
      where: {
        userId,
        gameId,
      },
    });

    if (existingPlayCount) {
      // Increment existing count
      await prisma.playCount.update({
        where: {
          id: existingPlayCount.id,
        },
        data: {
          count: existingPlayCount.count + 1,
        },
      });
    } else {
      // Create new play count record
      await prisma.playCount.create({
        data: {
          userId,
          gameId,
          count: 1,
        },
      });
    }

    return NextResponse.json({ message: "OK" });
  } catch (error) {
    console.error("Error incrementing play count:", error);
    return NextResponse.json(
      { message: "伺服器錯誤，請稍後再試" },
      { status: 500 },
    );
  }
}
